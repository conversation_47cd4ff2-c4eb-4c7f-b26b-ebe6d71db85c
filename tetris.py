import random
import time
import os
import sys

# 游戏常量
GRID_WIDTH = 10
GRID_HEIGHT = 20

# 字符定义
EMPTY = ' '
BLOCK = '█'
BORDER = '|'

# 方块形状定义
SHAPES = [
    # I形状
    [
        ['.....',
         '..#..',
         '..#..',
         '..#..',
         '..#..'],
        ['.....',
         '.....',
         '####.',
         '.....',
         '.....']
    ],
    # O形状
    [
        ['.....',
         '.....',
         '.##..',
         '.##..',
         '.....']
    ],
    # T形状
    [
        ['.....',
         '.....',
         '.#...',
         '###..',
         '.....'],
        ['.....',
         '.....',
         '.#...',
         '.##..',
         '.#...'],
        ['.....',
         '.....',
         '.....',
         '###..',
         '.#...'],
        ['.....',
         '.....',
         '.#...',
         '##...',
         '.#...']
    ],
    # S形状
    [
        ['.....',
         '.....',
         '.##..',
         '##...',
         '.....'],
        ['.....',
         '.#...',
         '.##..',
         '..#..',
         '.....']
    ],
    # Z形状
    [
        ['.....',
         '.....',
         '##...',
         '.##..',
         '.....'],
        ['.....',
         '..#..',
         '.##..',
         '.#...',
         '.....']
    ],
    # J形状
    [
        ['.....',
         '.#...',
         '.#...',
         '##...',
         '.....'],
        ['.....',
         '.....',
         '#....',
         '###..',
         '.....'],
        ['.....',
         '.##..',
         '.#...',
         '.#...',
         '.....'],
        ['.....',
         '.....',
         '###..',
         '..#..',
         '.....']
    ],
    # L形状
    [
        ['.....',
         '..#..',
         '..#..',
         '.##..',
         '.....'],
        ['.....',
         '.....',
         '###..',
         '#....',
         '.....'],
        ['.....',
         '##...',
         '.#...',
         '.#...',
         '.....'],
        ['.....',
         '.....',
         '..#..',
         '###..',
         '.....']
    ]
]

class Piece:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.shape = random.randint(0, len(SHAPES) - 1)
        self.rotation = 0

class Tetris:
    def __init__(self):
        self.grid = [[EMPTY for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]
        self.current_piece = self.new_piece()
        self.next_piece = self.new_piece()
        self.score = 0
        self.level = 1
        self.lines_cleared = 0
        self.game_over_flag = False
        self.running = True

    def new_piece(self):
        return Piece(GRID_WIDTH // 2 - 2, 0)

    def valid_move(self, piece, dx, dy, rotation):
        for y, row in enumerate(SHAPES[piece.shape][rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    nx, ny = piece.x + x + dx, piece.y + y + dy
                    if (nx < 0 or nx >= GRID_WIDTH or ny >= GRID_HEIGHT or
                        (ny >= 0 and self.grid[ny][nx] != EMPTY)):
                        return False
        return True

    def place_piece(self, piece):
        for y, row in enumerate(SHAPES[piece.shape][piece.rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    self.grid[piece.y + y][piece.x + x] = BLOCK

    def clear_lines(self):
        lines_to_clear = []
        for y in range(GRID_HEIGHT):
            if all(cell != EMPTY for cell in self.grid[y]):
                lines_to_clear.append(y)
        
        for y in lines_to_clear:
            del self.grid[y]
            self.grid.insert(0, [EMPTY for _ in range(GRID_WIDTH)])
        
        lines_cleared = len(lines_to_clear)
        self.lines_cleared += lines_cleared
        self.score += lines_cleared * 100 * self.level
        self.level = self.lines_cleared // 10 + 1

    def game_over(self):
        return not self.valid_move(self.current_piece, 0, 0, self.current_piece.rotation)

    def update(self):
        if self.valid_move(self.current_piece, 0, 1, self.current_piece.rotation):
            self.current_piece.y += 1
        else:
            self.place_piece(self.current_piece)
            self.clear_lines()
            self.current_piece = self.next_piece
            self.next_piece = self.new_piece()
            if self.game_over():
                self.game_over_flag = True

    def move_piece(self, dx):
        if self.valid_move(self.current_piece, dx, 0, self.current_piece.rotation):
            self.current_piece.x += dx

    def rotate_piece(self):
        new_rotation = (self.current_piece.rotation + 1) % len(SHAPES[self.current_piece.shape])
        if self.valid_move(self.current_piece, 0, 0, new_rotation):
            self.current_piece.rotation = new_rotation

    def drop_piece(self):
        while self.valid_move(self.current_piece, 0, 1, self.current_piece.rotation):
            self.current_piece.y += 1
        self.score += 2

    def get_display_grid(self):
        # 创建显示网格的副本
        display_grid = [row[:] for row in self.grid]
        
        # 添加当前方块到显示网格
        for y, row in enumerate(SHAPES[self.current_piece.shape][self.current_piece.rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    px, py = self.current_piece.x + x, self.current_piece.y + y
                    if 0 <= px < GRID_WIDTH and 0 <= py < GRID_HEIGHT:
                        display_grid[py][px] = BLOCK
        
        return display_grid

    def draw(self):
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("俄罗斯方块游戏")
        print("=" * 30)
        print(f"得分: {self.score}  等级: {self.level}  消除行数: {self.lines_cleared}")
        print("=" * 30)
        
        display_grid = self.get_display_grid()
        
        # 绘制游戏区域
        for y in range(GRID_HEIGHT):
            line = BORDER
            for x in range(GRID_WIDTH):
                line += display_grid[y][x]
            line += BORDER
            print(line)
        
        # 绘制底部边界
        print('+' + '-' * GRID_WIDTH + '+')
        
        # 显示下一个方块
        print("\n下一个方块:")
        for y in range(5):
            line = ""
            for x in range(5):
                if SHAPES[self.next_piece.shape][0][y][x] == '#':
                    line += BLOCK
                else:
                    line += ' '
            print(line)
        
        print("\n操作说明:")
        print("A/D - 左右移动")
        print("S - 加速下落")
        print("W - 旋转")
        print("空格 - 瞬间下落")
        print("Q - 退出游戏")
        
        if self.game_over_flag:
            print("\n游戏结束!")
            print("按任意键退出...")

def main():
    game = Tetris()

    print("俄罗斯方块游戏开始!")
    print("每次输入操作后按回车确认")
    print("操作说明: a-左移, d-右移, s-下移, w-旋转, space-瞬降, q-退出")
    print("=" * 50)

    move_count = 0

    while game.running and not game.game_over_flag:
        game.draw()

        # 自动下落逻辑
        move_count += 1
        if move_count >= 3:  # 每3次操作自动下落一次
            game.update()
            move_count = 0

        if game.game_over_flag:
            break

        try:
            user_input = input("\n请输入操作 (a/d/s/w/space/q): ").strip().lower()

            if user_input == 'a':
                game.move_piece(-1)
            elif user_input == 'd':
                game.move_piece(1)
            elif user_input == 's':
                if game.valid_move(game.current_piece, 0, 1, game.current_piece.rotation):
                    game.current_piece.y += 1
                    game.score += 1
            elif user_input == 'w':
                game.rotate_piece()
            elif user_input == 'space' or user_input == ' ':
                game.drop_piece()
            elif user_input == 'q':
                game.running = False
                break
            elif user_input == '':
                # 空输入，只是让方块自动下落
                pass
            else:
                print("无效输入，请重新输入!")
                continue

        except KeyboardInterrupt:
            print("\n游戏被中断!")
            break
        except EOFError:
            print("\n游戏结束!")
            break

    # 游戏结束
    game.draw()
    if game.game_over_flag:
        print(f"\n游戏结束! 最终得分: {game.score}")
        print(f"等级: {game.level}, 消除行数: {game.lines_cleared}")
    else:
        print("\n感谢游戏!")

if __name__ == "__main__":
    main()
