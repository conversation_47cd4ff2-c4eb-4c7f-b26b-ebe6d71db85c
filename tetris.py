import pygame
import random
import sys

# 初始化pygame
pygame.init()

# 游戏常量
GRID_WIDTH = 10
GRID_HEIGHT = 20
CELL_SIZE = 30
GRID_X_OFFSET = 50
GRID_Y_OFFSET = 50

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
CYAN = (0, 255, 255)
BLUE = (0, 0, 255)
ORANGE = (255, 165, 0)
YELLOW = (255, 255, 0)
GREEN = (0, 255, 0)
PURPLE = (128, 0, 128)
RED = (255, 0, 0)
GRAY = (128, 128, 128)

# 方块形状定义
SHAPES = [
    # I形状
    [['.....',
      '..#..',
      '..#..',
      '..#..',
      '..#..'],
     ['.....',
      '.....',
      '####.',
      '.....',
      '.....']],
    
    # O形状
    [['.....',
      '.....',
      '.##..',
      '.##..',
      '.....']],
    
    # T形状
    [['.....',
      '.....',
      '.#...',
      '###..',
      '.....'],
     ['.....',
      '.....',
      '.#...',
      '.##..',
      '.#...'],
     ['.....',
      '.....',
      '.....',
      '###..',
      '.#...'],
     ['.....',
      '.....',
      '.#...',
      '##...',
      '.#...']],
    
    # S形状
    [['.....',
      '.....',
      '.##..',
      '##...',
      '.....'],
     ['.....',
      '.#...',
      '.##..',
      '..#..',
      '.....']],
    
    # Z形状
    [['.....',
      '.....',
      '##...',
      '.##..',
      '.....'],
     ['.....',
      '..#..',
      '.##..',
      '.#...',
      '.....']],
    
    # J形状
    [['.....',
      '.#...',
      '.#...',
      '##...',
      '.....'],
     ['.....',
      '.....',
      '#....',
      '###..',
      '.....'],
     ['.....',
      '.##..',
      '.#...',
      '.#...',
      '.....'],
     ['.....',
      '.....',
      '###..',
      '..#..',
      '.....']],
    
    # L形状
    [['.....',
      '..#..',
      '..#..',
      '.##..',
      '.....'],
     ['.....',
      '.....',
      '###..',
      '#....',
      '.....'],
     ['.....',
      '##...',
      '.#...',
      '.#...',
      '.....'],
     ['.....',
      '.....',
      '..#..',
      '###..',
      '.....']]
]

SHAPE_COLORS = [CYAN, YELLOW, PURPLE, GREEN, RED, BLUE, ORANGE]

class Piece:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.shape = random.randint(0, len(SHAPES) - 1)
        self.color = SHAPE_COLORS[self.shape]
        self.rotation = 0

class Tetris:
    def __init__(self):
        self.grid = [[BLACK for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]
        self.current_piece = self.new_piece()
        self.next_piece = self.new_piece()
        self.score = 0
        self.level = 1
        self.lines_cleared = 0
        self.fall_time = 0
        self.fall_speed = 500  # 毫秒

    def new_piece(self):
        return Piece(GRID_WIDTH // 2 - 2, 0)

    def valid_move(self, piece, dx, dy, rotation):
        for y, row in enumerate(SHAPES[piece.shape][rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    nx, ny = piece.x + x + dx, piece.y + y + dy
                    if (nx < 0 or nx >= GRID_WIDTH or ny >= GRID_HEIGHT or
                        (ny >= 0 and self.grid[ny][nx] != BLACK)):
                        return False
        return True

    def place_piece(self, piece):
        for y, row in enumerate(SHAPES[piece.shape][piece.rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    self.grid[piece.y + y][piece.x + x] = piece.color

    def clear_lines(self):
        lines_to_clear = []
        for y in range(GRID_HEIGHT):
            if all(cell != BLACK for cell in self.grid[y]):
                lines_to_clear.append(y)
        
        for y in lines_to_clear:
            del self.grid[y]
            self.grid.insert(0, [BLACK for _ in range(GRID_WIDTH)])
        
        lines_cleared = len(lines_to_clear)
        self.lines_cleared += lines_cleared
        self.score += lines_cleared * 100 * self.level
        self.level = self.lines_cleared // 10 + 1
        self.fall_speed = max(50, 500 - (self.level - 1) * 50)

    def game_over(self):
        return not self.valid_move(self.current_piece, 0, 0, self.current_piece.rotation)

    def update(self, dt):
        self.fall_time += dt
        if self.fall_time >= self.fall_speed:
            if self.valid_move(self.current_piece, 0, 1, self.current_piece.rotation):
                self.current_piece.y += 1
            else:
                self.place_piece(self.current_piece)
                self.clear_lines()
                self.current_piece = self.next_piece
                self.next_piece = self.new_piece()
            self.fall_time = 0

    def move_piece(self, dx):
        if self.valid_move(self.current_piece, dx, 0, self.current_piece.rotation):
            self.current_piece.x += dx

    def rotate_piece(self):
        new_rotation = (self.current_piece.rotation + 1) % len(SHAPES[self.current_piece.shape])
        if self.valid_move(self.current_piece, 0, 0, new_rotation):
            self.current_piece.rotation = new_rotation

    def drop_piece(self):
        while self.valid_move(self.current_piece, 0, 1, self.current_piece.rotation):
            self.current_piece.y += 1
        self.score += 2

def draw_grid(screen, game):
    # 绘制游戏网格
    for y in range(GRID_HEIGHT):
        for x in range(GRID_WIDTH):
            rect = pygame.Rect(
                GRID_X_OFFSET + x * CELL_SIZE,
                GRID_Y_OFFSET + y * CELL_SIZE,
                CELL_SIZE,
                CELL_SIZE
            )
            pygame.draw.rect(screen, game.grid[y][x], rect)
            pygame.draw.rect(screen, WHITE, rect, 1)

def draw_piece(screen, piece):
    for y, row in enumerate(SHAPES[piece.shape][piece.rotation]):
        for x, cell in enumerate(row):
            if cell == '#':
                rect = pygame.Rect(
                    GRID_X_OFFSET + (piece.x + x) * CELL_SIZE,
                    GRID_Y_OFFSET + (piece.y + y) * CELL_SIZE,
                    CELL_SIZE,
                    CELL_SIZE
                )
                pygame.draw.rect(screen, piece.color, rect)
                pygame.draw.rect(screen, WHITE, rect, 1)

def draw_next_piece(screen, piece):
    # 绘制下一个方块预览
    start_x = GRID_X_OFFSET + GRID_WIDTH * CELL_SIZE + 20
    start_y = GRID_Y_OFFSET + 50
    
    for y, row in enumerate(SHAPES[piece.shape][piece.rotation]):
        for x, cell in enumerate(row):
            if cell == '#':
                rect = pygame.Rect(
                    start_x + x * CELL_SIZE // 2,
                    start_y + y * CELL_SIZE // 2,
                    CELL_SIZE // 2,
                    CELL_SIZE // 2
                )
                pygame.draw.rect(screen, piece.color, rect)
                pygame.draw.rect(screen, WHITE, rect, 1)

def draw_info(screen, game):
    font = pygame.font.Font(None, 36)
    
    # 绘制得分
    score_text = font.render(f"Score: {game.score}", True, WHITE)
    screen.blit(score_text, (GRID_X_OFFSET + GRID_WIDTH * CELL_SIZE + 20, GRID_Y_OFFSET))
    
    # 绘制等级
    level_text = font.render(f"Level: {game.level}", True, WHITE)
    screen.blit(level_text, (GRID_X_OFFSET + GRID_WIDTH * CELL_SIZE + 20, GRID_Y_OFFSET + 150))
    
    # 绘制消除行数
    lines_text = font.render(f"Lines: {game.lines_cleared}", True, WHITE)
    screen.blit(lines_text, (GRID_X_OFFSET + GRID_WIDTH * CELL_SIZE + 20, GRID_Y_OFFSET + 200))
    
    # 绘制下一个方块标题
    next_text = font.render("Next:", True, WHITE)
    screen.blit(next_text, (GRID_X_OFFSET + GRID_WIDTH * CELL_SIZE + 20, GRID_Y_OFFSET + 20))

def main():
    screen = pygame.display.set_mode((500, 700))
    pygame.display.set_caption("俄罗斯方块")
    clock = pygame.time.Clock()
    
    game = Tetris()
    
    while True:
        dt = clock.tick(60)
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_LEFT:
                    game.move_piece(-1)
                elif event.key == pygame.K_RIGHT:
                    game.move_piece(1)
                elif event.key == pygame.K_DOWN:
                    if game.valid_move(game.current_piece, 0, 1, game.current_piece.rotation):
                        game.current_piece.y += 1
                        game.score += 1
                elif event.key == pygame.K_UP:
                    game.rotate_piece()
                elif event.key == pygame.K_SPACE:
                    game.drop_piece()
        
        if not game.game_over():
            game.update(dt)
        
        screen.fill(BLACK)
        draw_grid(screen, game)
        draw_piece(screen, game.current_piece)
        draw_next_piece(screen, game.next_piece)
        draw_info(screen, game)
        
        if game.game_over():
            font = pygame.font.Font(None, 72)
            game_over_text = font.render("GAME OVER", True, RED)
            text_rect = game_over_text.get_rect(center=(250, 350))
            screen.blit(game_over_text, text_rect)
        
        pygame.display.flip()

if __name__ == "__main__":
    main()
