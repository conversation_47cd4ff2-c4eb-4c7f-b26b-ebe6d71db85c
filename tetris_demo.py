import random
import time
import os

# 游戏常量
GRID_WIDTH = 10
GRID_HEIGHT = 15

# 字符定义
EMPTY = '.'
BLOCK = '█'
BORDER = '|'

# 方块形状定义（简化版）
SHAPES = [
    # I形状
    [
        ['.....',
         '..#..',
         '..#..',
         '..#..',
         '..#..'],
        ['.....',
         '.....',
         '####.',
         '.....',
         '.....']
    ],
    # O形状
    [
        ['.....',
         '.....',
         '.##..',
         '.##..',
         '.....']
    ],
    # T形状
    [
        ['.....',
         '.....',
         '.#...',
         '###..',
         '.....'],
        ['.....',
         '.....',
         '.#...',
         '.##..',
         '.#...'],
        ['.....',
         '.....',
         '.....',
         '###..',
         '.#...'],
        ['.....',
         '.....',
         '.#...',
         '##...',
         '.#...']
    ],
    # L形状
    [
        ['.....',
         '..#..',
         '..#..',
         '.##..',
         '.....'],
        ['.....',
         '.....',
         '###..',
         '#....',
         '.....']
    ]
]

class Piece:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.shape = random.randint(0, len(SHAPES) - 1)
        self.rotation = 0

class TetrisDemo:
    def __init__(self):
        self.grid = [[EMPTY for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]
        self.current_piece = self.new_piece()
        self.score = 0
        self.lines_cleared = 0
        self.game_over_flag = False

    def new_piece(self):
        return Piece(GRID_WIDTH // 2 - 2, 0)

    def valid_move(self, piece, dx, dy, rotation):
        for y, row in enumerate(SHAPES[piece.shape][rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    nx, ny = piece.x + x + dx, piece.y + y + dy
                    if (nx < 0 or nx >= GRID_WIDTH or ny >= GRID_HEIGHT or
                        (ny >= 0 and self.grid[ny][nx] != EMPTY)):
                        return False
        return True

    def place_piece(self, piece):
        for y, row in enumerate(SHAPES[piece.shape][piece.rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    self.grid[piece.y + y][piece.x + x] = BLOCK

    def clear_lines(self):
        lines_to_clear = []
        for y in range(GRID_HEIGHT):
            if all(cell != EMPTY for cell in self.grid[y]):
                lines_to_clear.append(y)
        
        for y in lines_to_clear:
            del self.grid[y]
            self.grid.insert(0, [EMPTY for _ in range(GRID_WIDTH)])
        
        lines_cleared = len(lines_to_clear)
        self.lines_cleared += lines_cleared
        self.score += lines_cleared * 100

    def game_over(self):
        return not self.valid_move(self.current_piece, 0, 0, self.current_piece.rotation)

    def update(self):
        if self.valid_move(self.current_piece, 0, 1, self.current_piece.rotation):
            self.current_piece.y += 1
        else:
            self.place_piece(self.current_piece)
            self.clear_lines()
            self.current_piece = self.new_piece()
            if self.game_over():
                self.game_over_flag = True

    def get_display_grid(self):
        # 创建显示网格的副本
        display_grid = [row[:] for row in self.grid]
        
        # 添加当前方块到显示网格
        for y, row in enumerate(SHAPES[self.current_piece.shape][self.current_piece.rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    px, py = self.current_piece.x + x, self.current_piece.y + y
                    if 0 <= px < GRID_WIDTH and 0 <= py < GRID_HEIGHT:
                        display_grid[py][px] = BLOCK
        
        return display_grid

    def draw(self):
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("🎮 俄罗斯方块游戏演示 🎮")
        print("=" * 35)
        print(f"得分: {self.score}  消除行数: {self.lines_cleared}")
        print("=" * 35)
        
        display_grid = self.get_display_grid()
        
        # 绘制游戏区域
        print("+" + "-" * GRID_WIDTH + "+")
        for y in range(GRID_HEIGHT):
            line = BORDER
            for x in range(GRID_WIDTH):
                line += display_grid[y][x]
            line += BORDER
            print(line)
        
        # 绘制底部边界
        print("+" + "-" * GRID_WIDTH + "+")
        
        if self.game_over_flag:
            print("\n🎯 游戏结束!")
            print(f"最终得分: {self.score}")
            print(f"消除行数: {self.lines_cleared}")

def demo_game():
    """自动演示游戏"""
    game = TetrisDemo()
    
    print("🚀 俄罗斯方块游戏自动演示开始!")
    print("游戏将自动运行，展示游戏效果...")
    time.sleep(2)
    
    step = 0
    auto_moves = ['left', 'right', 'rotate', 'down', 'down']
    
    while not game.game_over_flag and step < 50:  # 限制演示步数
        game.draw()
        
        # 自动操作
        if step % 5 == 0:  # 每5步尝试一次随机操作
            action = random.choice(auto_moves)
            if action == 'left' and game.valid_move(game.current_piece, -1, 0, game.current_piece.rotation):
                game.current_piece.x -= 1
                print("⬅️ 自动左移")
            elif action == 'right' and game.valid_move(game.current_piece, 1, 0, game.current_piece.rotation):
                game.current_piece.x += 1
                print("➡️ 自动右移")
            elif action == 'rotate':
                new_rotation = (game.current_piece.rotation + 1) % len(SHAPES[game.current_piece.shape])
                if game.valid_move(game.current_piece, 0, 0, new_rotation):
                    game.current_piece.rotation = new_rotation
                    print("🔄 自动旋转")
        
        # 自动下落
        game.update()
        print("⬇️ 方块下落")
        
        time.sleep(1)  # 暂停1秒观察效果
        step += 1
    
    # 最终显示
    game.draw()
    print("\n✨ 演示结束!")
    print("这就是俄罗斯方块游戏的基本效果!")

def interactive_game():
    """交互式游戏"""
    game = TetrisDemo()
    
    print("🎮 俄罗斯方块交互式游戏!")
    print("操作说明:")
    print("a - 左移, d - 右移, s - 下移, w - 旋转")
    print("直接按回车 - 自动下落")
    print("q - 退出游戏")
    print("=" * 40)
    
    while not game.game_over_flag:
        game.draw()
        
        try:
            user_input = input("\n请输入操作: ").strip().lower()
            
            if user_input == 'a':
                if game.valid_move(game.current_piece, -1, 0, game.current_piece.rotation):
                    game.current_piece.x -= 1
            elif user_input == 'd':
                if game.valid_move(game.current_piece, 1, 0, game.current_piece.rotation):
                    game.current_piece.x += 1
            elif user_input == 's':
                if game.valid_move(game.current_piece, 0, 1, game.current_piece.rotation):
                    game.current_piece.y += 1
            elif user_input == 'w':
                new_rotation = (game.current_piece.rotation + 1) % len(SHAPES[game.current_piece.shape])
                if game.valid_move(game.current_piece, 0, 0, new_rotation):
                    game.current_piece.rotation = new_rotation
            elif user_input == 'q':
                break
            else:
                # 空输入或其他，自动下落
                game.update()
                
        except KeyboardInterrupt:
            print("\n游戏被中断!")
            break
    
    game.draw()
    print(f"\n游戏结束! 最终得分: {game.score}")

def main():
    print("🎮 欢迎来到俄罗斯方块游戏! 🎮")
    print("\n请选择游戏模式:")
    print("1. 自动演示模式 (推荐)")
    print("2. 交互式游戏模式")
    
    try:
        choice = input("\n请输入选择 (1 或 2): ").strip()
        
        if choice == '1':
            demo_game()
        elif choice == '2':
            interactive_game()
        else:
            print("自动选择演示模式...")
            demo_game()
            
    except KeyboardInterrupt:
        print("\n程序退出!")
    except:
        print("自动运行演示模式...")
        demo_game()

if __name__ == "__main__":
    main()
