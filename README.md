# 俄罗斯方块游戏

这是一个使用Python和Pygame开发的经典俄罗斯方块游戏。

## 安装依赖

```bash
pip install pygame
```

或者使用requirements.txt：

```bash
pip install -r requirements.txt
```

## 运行游戏

```bash
python tetris.py
```

## 游戏操作

- **左箭头键**: 向左移动方块
- **右箭头键**: 向右移动方块
- **下箭头键**: 加速下落
- **上箭头键**: 旋转方块
- **空格键**: 瞬间下落到底部

## 游戏特性

- 7种经典俄罗斯方块形状（I、O、T、S、Z、J、L）
- 自动消除完整行
- 得分系统
- 等级系统（随着消除行数增加，下落速度加快）
- 下一个方块预览
- 游戏结束检测

## 得分规则

- 消除1行：100分 × 当前等级
- 软下落（按下箭头）：每格1分
- 硬下落（空格键）：每格2分
- 每消除10行，等级提升1级，下落速度加快

## 游戏界面

- 左侧：游戏主区域（10×20网格）
- 右侧：显示得分、等级、消除行数和下一个方块预览

祝您游戏愉快！
