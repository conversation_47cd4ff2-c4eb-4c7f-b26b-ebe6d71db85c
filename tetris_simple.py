import random

# 游戏常量
GRID_WIDTH = 8
GRID_HEIGHT = 12

# 字符定义
EMPTY = '.'
BLOCK = '#'

# 简化的方块形状
SHAPES = [
    # I形状
    [
        ['..#.',
         '..#.',
         '..#.',
         '..#.'],
        ['....',
         '####',
         '....',
         '....']
    ],
    # O形状
    [
        ['.##.',
         '.##.',
         '....',
         '....']
    ],
    # T形状
    [
        ['.#..',
         '###.',
         '....',
         '....'],
        ['.#..',
         '.##.',
         '.#..',
         '....']
    ],
    # L形状
    [
        ['..#.',
         '..#.',
         '.##.',
         '....'],
        ['....',
         '###.',
         '#...',
         '....']
    ]
]

class Piece:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.shape = random.randint(0, len(SHAPES) - 1)
        self.rotation = 0

class SimpleTetris:
    def __init__(self):
        self.grid = [[EMPTY for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]
        self.current_piece = self.new_piece()
        self.score = 0
        self.lines_cleared = 0
        self.step = 0

    def new_piece(self):
        return Piece(GRID_WIDTH // 2 - 2, 0)

    def valid_move(self, piece, dx, dy, rotation):
        shape = SHAPES[piece.shape][rotation]
        for y, row in enumerate(shape):
            for x, cell in enumerate(row):
                if cell == '#':
                    nx, ny = piece.x + x + dx, piece.y + y + dy
                    if (nx < 0 or nx >= GRID_WIDTH or ny >= GRID_HEIGHT or
                        (ny >= 0 and self.grid[ny][nx] != EMPTY)):
                        return False
        return True

    def place_piece(self, piece):
        shape = SHAPES[piece.shape][piece.rotation]
        for y, row in enumerate(shape):
            for x, cell in enumerate(row):
                if cell == '#':
                    if piece.y + y >= 0:
                        self.grid[piece.y + y][piece.x + x] = BLOCK

    def clear_lines(self):
        lines_to_clear = []
        for y in range(GRID_HEIGHT):
            if all(cell != EMPTY for cell in self.grid[y]):
                lines_to_clear.append(y)
        
        for y in lines_to_clear:
            del self.grid[y]
            self.grid.insert(0, [EMPTY for _ in range(GRID_WIDTH)])
        
        lines_cleared = len(lines_to_clear)
        self.lines_cleared += lines_cleared
        self.score += lines_cleared * 100
        
        return lines_cleared

    def update(self):
        if self.valid_move(self.current_piece, 0, 1, self.current_piece.rotation):
            self.current_piece.y += 1
            return False
        else:
            self.place_piece(self.current_piece)
            cleared = self.clear_lines()
            self.current_piece = self.new_piece()
            return True, cleared

    def get_display_grid(self):
        display_grid = [row[:] for row in self.grid]
        
        # 添加当前方块
        shape = SHAPES[self.current_piece.shape][self.current_piece.rotation]
        for y, row in enumerate(shape):
            for x, cell in enumerate(row):
                if cell == '#':
                    px, py = self.current_piece.x + x, self.current_piece.y + y
                    if 0 <= px < GRID_WIDTH and 0 <= py < GRID_HEIGHT:
                        display_grid[py][px] = BLOCK
        
        return display_grid

    def print_game(self):
        print(f"\n=== 俄罗斯方块 第{self.step}步 ===")
        print(f"得分: {self.score} | 消除行数: {self.lines_cleared}")
        print("+" + "-" * GRID_WIDTH + "+")
        
        display_grid = self.get_display_grid()
        for row in display_grid:
            print("|" + "".join(row) + "|")
        
        print("+" + "-" * GRID_WIDTH + "+")

def demo():
    print("🎮 俄罗斯方块游戏演示")
    print("=" * 30)
    
    game = SimpleTetris()
    
    for step in range(20):
        game.step = step + 1
        game.print_game()
        
        # 随机操作
        if step % 3 == 0:
            # 尝试左右移动
            direction = random.choice([-1, 1])
            if game.valid_move(game.current_piece, direction, 0, game.current_piece.rotation):
                game.current_piece.x += direction
                print(f"→ 方块{'左' if direction == -1 else '右'}移")
        
        elif step % 3 == 1:
            # 尝试旋转
            new_rotation = (game.current_piece.rotation + 1) % len(SHAPES[game.current_piece.shape])
            if game.valid_move(game.current_piece, 0, 0, new_rotation):
                game.current_piece.rotation = new_rotation
                print("→ 方块旋转")
        
        # 方块下落
        result = game.update()
        if isinstance(result, tuple):
            placed, cleared = result
            if placed:
                print(f"→ 方块放置完成")
                if cleared > 0:
                    print(f"🎉 消除了 {cleared} 行!")
        else:
            print("→ 方块下落")
        
        # 检查游戏结束
        if not game.valid_move(game.current_piece, 0, 0, game.current_piece.rotation):
            print("\n💥 游戏结束!")
            break
        
        print("-" * 30)
    
    print(f"\n🎯 游戏演示结束!")
    print(f"最终得分: {game.score}")
    print(f"消除行数: {game.lines_cleared}")
    print(f"演示步数: {game.step}")
    print("\n✨ 这就是俄罗斯方块的基本玩法!")
    print("方块会自动下落，玩家可以左右移动和旋转方块")
    print("当一行被完全填满时会被消除并得分")

if __name__ == "__main__":
    demo()
