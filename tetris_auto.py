import random
import time
import os

# 游戏常量
GRID_WIDTH = 10
GRID_HEIGHT = 15

# 字符定义
EMPTY = '.'
BLOCK = '█'

# 方块形状定义
SHAPES = [
    # I形状
    [
        ['.....',
         '..#..',
         '..#..',
         '..#..',
         '..#..'],
        ['.....',
         '.....',
         '####.',
         '.....',
         '.....']
    ],
    # O形状
    [
        ['.....',
         '.....',
         '.##..',
         '.##..',
         '.....']
    ],
    # T形状
    [
        ['.....',
         '.....',
         '.#...',
         '###..',
         '.....'],
        ['.....',
         '.....',
         '.#...',
         '.##..',
         '.#...']
    ],
    # L形状
    [
        ['.....',
         '..#..',
         '..#..',
         '.##..',
         '.....'],
        ['.....',
         '.....',
         '###..',
         '#....',
         '.....']
    ]
]

class Piece:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.shape = random.randint(0, len(SHAPES) - 1)
        self.rotation = 0

class TetrisAuto:
    def __init__(self):
        self.grid = [[EMPTY for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]
        self.current_piece = self.new_piece()
        self.score = 0
        self.lines_cleared = 0
        self.pieces_placed = 0

    def new_piece(self):
        return Piece(GRID_WIDTH // 2 - 2, 0)

    def valid_move(self, piece, dx, dy, rotation):
        for y, row in enumerate(SHAPES[piece.shape][rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    nx, ny = piece.x + x + dx, piece.y + y + dy
                    if (nx < 0 or nx >= GRID_WIDTH or ny >= GRID_HEIGHT or
                        (ny >= 0 and self.grid[ny][nx] != EMPTY)):
                        return False
        return True

    def place_piece(self, piece):
        for y, row in enumerate(SHAPES[piece.shape][piece.rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    self.grid[piece.y + y][piece.x + x] = BLOCK
        self.pieces_placed += 1

    def clear_lines(self):
        lines_to_clear = []
        for y in range(GRID_HEIGHT):
            if all(cell != EMPTY for cell in self.grid[y]):
                lines_to_clear.append(y)
        
        for y in lines_to_clear:
            del self.grid[y]
            self.grid.insert(0, [EMPTY for _ in range(GRID_WIDTH)])
        
        lines_cleared = len(lines_to_clear)
        self.lines_cleared += lines_cleared
        self.score += lines_cleared * 100
        
        if lines_cleared > 0:
            print(f"🎉 消除了 {lines_cleared} 行! 得分 +{lines_cleared * 100}")

    def update(self):
        if self.valid_move(self.current_piece, 0, 1, self.current_piece.rotation):
            self.current_piece.y += 1
            return False  # 还在下落
        else:
            self.place_piece(self.current_piece)
            self.clear_lines()
            self.current_piece = self.new_piece()
            return True  # 放置了新方块

    def get_display_grid(self):
        display_grid = [row[:] for row in self.grid]
        
        # 添加当前方块
        for y, row in enumerate(SHAPES[self.current_piece.shape][self.current_piece.rotation]):
            for x, cell in enumerate(row):
                if cell == '#':
                    px, py = self.current_piece.x + x, self.current_piece.y + y
                    if 0 <= px < GRID_WIDTH and 0 <= py < GRID_HEIGHT:
                        display_grid[py][px] = BLOCK
        
        return display_grid

    def draw(self):
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("🎮 俄罗斯方块自动演示 🎮")
        print("=" * 35)
        print(f"得分: {self.score}")
        print(f"消除行数: {self.lines_cleared}")
        print(f"放置方块数: {self.pieces_placed}")
        print("=" * 35)
        
        display_grid = self.get_display_grid()
        
        # 绘制游戏区域
        print("+" + "-" * GRID_WIDTH + "+")
        for y in range(GRID_HEIGHT):
            line = "|"
            for x in range(GRID_WIDTH):
                line += display_grid[y][x]
            line += "|"
            print(line)
        print("+" + "-" * GRID_WIDTH + "+")

    def auto_play(self):
        """智能自动游戏"""
        # 简单的AI策略：尝试将方块放在最佳位置
        best_x = self.current_piece.x
        best_rotation = self.current_piece.rotation
        
        # 尝试不同的旋转和位置
        for rotation in range(len(SHAPES[self.current_piece.shape])):
            for x in range(GRID_WIDTH):
                if self.valid_move(self.current_piece, x - self.current_piece.x, 0, rotation):
                    # 计算这个位置的"好坏"（简单评估）
                    test_y = self.current_piece.y
                    while self.valid_move(self.current_piece, x - self.current_piece.x, test_y - self.current_piece.y + 1, rotation):
                        test_y += 1
                    
                    # 偏好放在底部和中间位置
                    score = test_y + (5 - abs(x - GRID_WIDTH // 2))
                    if score > (self.current_piece.y + (5 - abs(best_x - GRID_WIDTH // 2))):
                        best_x = x
                        best_rotation = rotation
        
        # 应用最佳策略
        self.current_piece.rotation = best_rotation
        self.current_piece.x = best_x

def main():
    print("🚀 俄罗斯方块自动演示开始!")
    print("程序将自动运行，展示游戏效果...")
    print("按 Ctrl+C 可以随时停止")
    print("=" * 40)
    
    time.sleep(2)
    
    game = TetrisAuto()
    step = 0
    
    try:
        while step < 30:  # 演示30步
            game.draw()
            
            print(f"\n第 {step + 1} 步:")
            
            # 自动智能游戏
            if step % 3 == 0:  # 每3步使用AI策略
                game.auto_play()
                print("🤖 AI选择最佳位置")
            else:
                # 随机移动
                actions = ['left', 'right', 'rotate']
                action = random.choice(actions)
                
                if action == 'left' and game.valid_move(game.current_piece, -1, 0, game.current_piece.rotation):
                    game.current_piece.x -= 1
                    print("⬅️ 左移")
                elif action == 'right' and game.valid_move(game.current_piece, 1, 0, game.current_piece.rotation):
                    game.current_piece.x += 1
                    print("➡️ 右移")
                elif action == 'rotate':
                    new_rotation = (game.current_piece.rotation + 1) % len(SHAPES[game.current_piece.shape])
                    if game.valid_move(game.current_piece, 0, 0, new_rotation):
                        game.current_piece.rotation = new_rotation
                        print("🔄 旋转")
            
            # 方块下落
            placed = game.update()
            if placed:
                print("📦 方块已放置")
            else:
                print("⬇️ 方块下落")
            
            # 检查游戏是否结束
            if not game.valid_move(game.current_piece, 0, 0, game.current_piece.rotation):
                print("💥 游戏结束!")
                break
            
            time.sleep(1.5)  # 暂停观察
            step += 1
        
        # 最终结果
        game.draw()
        print("\n🎯 演示完成!")
        print(f"最终得分: {game.score}")
        print(f"消除行数: {game.lines_cleared}")
        print(f"放置方块数: {game.pieces_placed}")
        print("\n✨ 这就是俄罗斯方块游戏的效果!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 演示被用户停止")
        game.draw()
        print(f"当前得分: {game.score}")
    
    print("\n感谢观看俄罗斯方块演示! 🎮")

if __name__ == "__main__":
    main()
